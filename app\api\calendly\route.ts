import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const trainerId = searchParams.get('trainerId');

    switch (action) {
      case 'trainer-settings': {
        // Get trainer's Calendly settings
        if (!trainerId) {
          return NextResponse.json({ error: 'Trainer ID is required' }, { status: 400 });
        }

        const trainer = await prisma.user.findUnique({
          where: {
            id: trainerId,
          },
          select: {
            id: true,
            name: true,
            calendlyUserId: true,
          },
        });

        if (!trainer) {
          return NextResponse.json({ error: 'Trainer not found' }, { status: 404 });
        }

        return NextResponse.json({ trainer });
      }

      case 'my-settings': {
        // Get current user's Calendly settings
        const user = await prisma.user.findUnique({
          where: {
            id: session.user.id,
          },
          select: {
            id: true,
            name: true,
            calendlyUserId: true,
          },
        });

        if (!user) {
          return NextResponse.json({ error: 'User not found' }, { status: 404 });
        }

        return NextResponse.json({ user });
      }

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in Calendly API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const body = await request.json();

    switch (action) {
      case 'update-settings': {
        // Update user's Calendly settings
        const { calendlyUrl, calendlyUserId } = body;

        if (!calendlyUrl) {
          return NextResponse.json({ error: 'Calendly URL is required' }, { status: 400 });
        }

        // Extract Calendly username from URL if userId not provided
        let userId = calendlyUserId;
        if (!userId && calendlyUrl) {
          const match = calendlyUrl.match(/calendly\.com\/([^/]+)/);
          userId = match ? match[1] : null;
        }

        const updatedUser = await prisma.user.update({
          where: {
            id: session.user.id,
          },
          data: {
            calendlyUserId: userId,
          },
        });

        return NextResponse.json({ success: true, user: updatedUser });
      }

      case 'sync-events': {
        // Trigger a sync of Calendly events for the current user
        try {
          const response = await fetch(`${request.url.split('/api')[0]}/api/calendly/sync`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Cookie': request.headers.get('Cookie') || ''
            },
            body: JSON.stringify({ trainerId: session.user.id })
          });

          const syncResult = await response.json();

          if (!response.ok) {
            return NextResponse.json({ error: syncResult.error || 'Sync failed' }, { status: response.status });
          }

          return NextResponse.json(syncResult);
        } catch (error) {
          console.error('Error triggering sync:', error);
          return NextResponse.json({ error: 'Failed to trigger sync' }, { status: 500 });
        }
      }

      case 'webhook': {
        // Handle Calendly webhook events
        const { event } = body;

        if (!event) {
          return NextResponse.json({ error: 'Event data is required' }, { status: 400 });
        }

        // Process the event based on its type
        switch (event.type) {
          case 'invitee.created':
            // Handle new booking
            await handleNewBooking(event);
            break;
          case 'invitee.canceled':
            // Handle cancellation
            await handleCancellation(event);
            break;
          case 'invitee.rescheduled':
            // Handle reschedule
            await handleReschedule(event);
            break;
          default:
            console.log('Unhandled event type:', event.type);
        }

        return NextResponse.json({ success: true });
      }

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in Calendly API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Helper functions for handling Calendly webhook events
async function handleNewBooking(event: any) {
  try {
    const { invitee, event_type, event: calendlyEvent } = event;
    
    // Extract trainer's Calendly user ID from the event
    const calendlyUserId = event_type.owner;
    
    // Find the trainer by Calendly user ID
    const trainer = await prisma.user.findFirst({
      where: {
        calendlyUserId,
      },
    });
    
    if (!trainer) {
      console.error('Trainer not found for Calendly user ID:', calendlyUserId);
      return;
    }
    
    // Extract client email from invitee
    const clientEmail = invitee.email;
    
    // Find the client by email
    const client = await prisma.user.findUnique({
      where: {
        email: clientEmail,
      },
    });
    
    if (!client) {
      console.error('Client not found for email:', clientEmail);
      return;
    }
    
    // Find or create coaching relationship
    let coachingRelationship = await prisma.coachingRelationship.findFirst({
      where: {
        trainerId: trainer.id,
        clientId: client.id,
        status: 'active',
      },
    });
    
    if (!coachingRelationship) {
      coachingRelationship = await prisma.coachingRelationship.create({
        data: {
          trainerId: trainer.id,
          clientId: client.id,
          status: 'active',
          calendlyUserId,
        },
      });
    }
    
    // Create coaching session
    await prisma.coachingSession.create({
      data: {
        coachingRelationshipId: coachingRelationship.id,
        title: event_type.name,
        description: event_type.description || '',
        scheduledDate: new Date(calendlyEvent.start_time),
        duration: (new Date(calendlyEvent.end_time).getTime() - new Date(calendlyEvent.start_time).getTime()) / 60000, // Convert to minutes
        status: 'scheduled',
        type: 'video',
        location: calendlyEvent.location?.join(', ') || '',
        calendlyEventId: calendlyEvent.uuid,
        calendlyEventUri: calendlyEvent.uri,
        videoConferenceUrl: calendlyEvent.location_info?.join_url || '',
      },
    });
    
    console.log('Successfully created coaching session from Calendly booking');
  } catch (error) {
    console.error('Error handling new booking:', error);
  }
}

async function handleCancellation(event: any) {
  try {
    const { event: calendlyEvent } = event;
    
    // Find the coaching session by Calendly event ID
    const session = await prisma.coachingSession.findFirst({
      where: {
        calendlyEventId: calendlyEvent.uuid,
      },
    });
    
    if (!session) {
      console.error('Coaching session not found for Calendly event ID:', calendlyEvent.uuid);
      return;
    }
    
    // Update session status
    await prisma.coachingSession.update({
      where: {
        id: session.id,
      },
      data: {
        status: 'cancelled',
      },
    });
    
    console.log('Successfully updated coaching session status to cancelled');
  } catch (error) {
    console.error('Error handling cancellation:', error);
  }
}

async function handleReschedule(event: any) {
  try {
    const { event: calendlyEvent } = event;
    
    // Find the coaching session by Calendly event ID
    const session = await prisma.coachingSession.findFirst({
      where: {
        calendlyEventId: calendlyEvent.uuid,
      },
    });
    
    if (!session) {
      console.error('Coaching session not found for Calendly event ID:', calendlyEvent.uuid);
      return;
    }
    
    // Update session with new schedule
    await prisma.coachingSession.update({
      where: {
        id: session.id,
      },
      data: {
        scheduledDate: new Date(calendlyEvent.start_time),
        duration: (new Date(calendlyEvent.end_time).getTime() - new Date(calendlyEvent.start_time).getTime()) / 60000, // Convert to minutes
        status: 'rescheduled',
      },
    });
    
    console.log('Successfully updated coaching session with new schedule');
  } catch (error) {
    console.error('Error handling reschedule:', error);
  }
}
