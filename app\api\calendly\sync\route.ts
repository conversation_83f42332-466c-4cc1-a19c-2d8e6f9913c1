import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { calendlyApi } from '@/lib/calendly-api';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { trainerId } = await request.json();

    // If trainerId is provided, check if user is admin or the trainer themselves
    let targetTrainerId = session.user.id;
    if (trainerId) {
      if (session.user.role !== 'admin' && session.user.id !== trainerId) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }
      targetTrainerId = trainerId;
    }

    // Get trainer's Calendly settings
    const trainer = await prisma.user.findUnique({
      where: { id: targetTrainerId },
      select: { 
        id: true, 
        calendlyUserId: true, 
        name: true, 
        email: true 
      }
    });

    if (!trainer) {
      return NextResponse.json({ error: 'Trainer not found' }, { status: 404 });
    }

    if (!trainer.calendlyUserId) {
      return NextResponse.json({ 
        error: 'Calendly integration not configured for this trainer' 
      }, { status: 400 });
    }

    // Fetch events from Calendly API
    const calendlyEvents = await calendlyApi.getScheduledEvents(trainer.calendlyUserId, {
      startTime: new Date(), // From now
      endTime: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // Next 90 days
      status: 'active'
    });

    let syncedCount = 0;
    let skippedCount = 0;
    const errors: string[] = [];

    for (const calendlyEvent of calendlyEvents) {
      try {
        const eventId = calendlyApi.extractEventId(calendlyEvent.uri);
        
        // Check if this event already exists
        const existingSession = await prisma.coachingSession.findFirst({
          where: { calendlyEventId: eventId }
        });

        if (existingSession) {
          skippedCount++;
          continue;
        }

        // Get event invitees to find the client
        const invitees = await calendlyApi.getEventInvitees(calendlyEvent.uri);
        
        if (invitees.length === 0) {
          errors.push(`No invitees found for event ${eventId}`);
          continue;
        }

        // Find the client by email (assuming first invitee is the client)
        const clientEmail = invitees[0].email;
        const client = await prisma.user.findUnique({
          where: { email: clientEmail }
        });

        if (!client) {
          // Create a basic client record if they don't exist
          // In production, you might want to handle this differently
          errors.push(`Client not found for email: ${clientEmail}`);
          continue;
        }

        // Find or create coaching relationship
        let coachingRelationship = await prisma.coachingRelationship.findFirst({
          where: {
            trainerId: trainer.id,
            clientId: client.id,
            status: 'active'
          }
        });

        if (!coachingRelationship) {
          coachingRelationship = await prisma.coachingRelationship.create({
            data: {
              trainerId: trainer.id,
              clientId: client.id,
              status: 'active',
              expirationDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year from now
            }
          });
        }

        // Calculate duration in minutes
        const startTime = new Date(calendlyEvent.start_time);
        const endTime = new Date(calendlyEvent.end_time);
        const duration = Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60));

        // Create coaching session
        await prisma.coachingSession.create({
          data: {
            coachingRelationshipId: coachingRelationship.id,
            title: calendlyEvent.name,
            description: calendlyEvent.meeting_notes_plain || '',
            scheduledDate: startTime,
            duration,
            status: calendlyEvent.status === 'active' ? 'scheduled' : 'cancelled',
            type: 'video',
            location: calendlyEvent.location?.location || '',
            calendlyEventId: eventId,
            calendlyEventUri: calendlyEvent.uri,
            videoConferenceUrl: calendlyEvent.location?.join_url || ''
          }
        });

        syncedCount++;
      } catch (error) {
        console.error(`Error syncing event ${calendlyEvent.uri}:`, error);
        errors.push(`Failed to sync event: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return NextResponse.json({
      success: true,
      message: `Sync completed. ${syncedCount} events synced, ${skippedCount} skipped.`,
      syncedCount,
      skippedCount,
      errors: errors.length > 0 ? errors : undefined
    });

  } catch (error) {
    console.error('Error in Calendly sync:', error);
    return NextResponse.json(
      { error: 'Internal server error during sync' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const trainerId = searchParams.get('trainerId') || session.user.id;

    // Check permissions
    if (session.user.role !== 'admin' && session.user.id !== trainerId) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get sync status/history for the trainer
    const trainer = await prisma.user.findUnique({
      where: { id: trainerId },
      select: { 
        id: true, 
        calendlyUserId: true, 
        name: true,
        updatedAt: true
      }
    });

    if (!trainer) {
      return NextResponse.json({ error: 'Trainer not found' }, { status: 404 });
    }

    // Get recent sessions synced from Calendly
    const recentSyncedSessions = await prisma.coachingSession.findMany({
      where: {
        coachingRelationship: {
          trainerId: trainer.id
        },
        calendlyEventId: {
          not: null
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10,
      include: {
        coachingRelationship: {
          include: {
            client: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        }
      }
    });

    return NextResponse.json({
      trainer,
      isConfigured: !!trainer.calendlyUserId,
      recentSyncedSessions,
      lastSyncAttempt: trainer.updatedAt
    });

  } catch (error) {
    console.error('Error getting sync status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
