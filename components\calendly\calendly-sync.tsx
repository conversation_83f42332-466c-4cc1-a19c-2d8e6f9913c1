'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, RefreshCw, Calendar, Clock, User, Video } from 'lucide-react';
import { format } from 'date-fns';

interface SyncedSession {
  id: string;
  title: string;
  description: string | null;
  scheduledDate: string;
  duration: number;
  status: string;
  videoConferenceUrl: string | null;
  calendlyEventId: string | null;
  coachingRelationship: {
    client: {
      id: string;
      name: string;
      email: string;
    };
  };
}

interface CalendlySyncProps {
  trainerId?: string;
  onSyncComplete?: (result: any) => void;
}

export function CalendlySync({ trainerId, onSyncComplete }: CalendlySyncProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [syncResult, setSyncResult] = useState<any>(null);
  const [recentSessions, setRecentSessions] = useState<SyncedSession[]>([]);
  const { toast } = useToast();

  const handleSync = async () => {
    try {
      setIsLoading(true);
      setSyncResult(null);

      const response = await fetch('/api/calendly/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ trainerId }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Sync failed');
      }

      setSyncResult(result);
      
      toast({
        title: 'Sync completed',
        description: result.message,
      });

      // Fetch updated sessions
      await fetchRecentSessions();

      if (onSyncComplete) {
        onSyncComplete(result);
      }
    } catch (error) {
      console.error('Error syncing Calendly events:', error);
      toast({
        variant: 'destructive',
        title: 'Sync failed',
        description: error instanceof Error ? error.message : 'Failed to sync Calendly events',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchRecentSessions = async () => {
    try {
      const response = await fetch(`/api/calendly/sync?trainerId=${trainerId || ''}`);
      
      if (response.ok) {
        const data = await response.json();
        setRecentSessions(data.recentSyncedSessions || []);
      }
    } catch (error) {
      console.error('Error fetching recent sessions:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'rescheduled':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5" />
            Calendly Sync
          </CardTitle>
          <CardDescription>
            Sync your Calendly scheduled sessions to your local dashboard. This will fetch all upcoming sessions
            and add them to your coaching sessions list.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button 
            onClick={handleSync} 
            disabled={isLoading}
            className="w-full sm:w-auto"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Syncing...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Sync Calendly Events
              </>
            )}
          </Button>

          {syncResult && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h4 className="font-medium text-green-800 mb-2">Sync Results</h4>
              <p className="text-sm text-green-700">{syncResult.message}</p>
              {syncResult.errors && syncResult.errors.length > 0 && (
                <div className="mt-2">
                  <p className="text-sm font-medium text-red-700">Errors:</p>
                  <ul className="text-sm text-red-600 list-disc list-inside">
                    {syncResult.errors.map((error: string, index: number) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {recentSessions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Recently Synced Sessions
            </CardTitle>
            <CardDescription>
              Sessions that have been synced from your Calendly account
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentSessions.map((session) => (
                <div
                  key={session.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium">{session.title}</h4>
                      <Badge className={getStatusColor(session.status)}>
                        {session.status}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <User className="h-4 w-4" />
                        {session.coachingRelationship.client.name}
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        {format(new Date(session.scheduledDate), 'MMM d, yyyy')}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {format(new Date(session.scheduledDate), 'h:mm a')} ({session.duration}m)
                      </div>
                      {session.videoConferenceUrl && (
                        <div className="flex items-center gap-1">
                          <Video className="h-4 w-4" />
                          Video call
                        </div>
                      )}
                    </div>
                    
                    {session.description && (
                      <p className="text-sm text-gray-500 mt-1">{session.description}</p>
                    )}
                  </div>
                  
                  {session.videoConferenceUrl && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(session.videoConferenceUrl!, '_blank')}
                    >
                      Join Call
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
