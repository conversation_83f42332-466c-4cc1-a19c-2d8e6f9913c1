/**
 * Calendly API service for fetching scheduled events
 * This service uses Calendly's public API to fetch events for a specific user
 * without requiring webhook secrets or personal access tokens
 */

interface CalendlyEvent {
  uri: string;
  name: string;
  meeting_notes_plain?: string;
  meeting_notes_html?: string;
  status: string;
  start_time: string;
  end_time: string;
  event_type: string;
  location?: {
    type: string;
    location?: string;
    join_url?: string;
  };
  invitees_counter: {
    total: number;
    active: number;
    limit: number;
  };
  created_at: string;
  updated_at: string;
}

interface CalendlyEventType {
  uri: string;
  name: string;
  description_plain?: string;
  description_html?: string;
  duration: number;
  scheduling_url: string;
  owner: string;
}

interface CalendlyInvitee {
  uri: string;
  name: string;
  email: string;
  status: string;
  created_at: string;
  updated_at: string;
  cancel_url?: string;
  reschedule_url?: string;
  event: string;
}

interface CalendlyApiResponse<T> {
  collection: T[];
  pagination: {
    count: number;
    next_page?: string;
    previous_page?: string;
    next_page_token?: string;
    previous_page_token?: string;
  };
}

export class CalendlyApiService {
  private baseUrl = 'https://api.calendly.com';
  
  /**
   * Get scheduled events for a Calendly user
   * Note: This requires a personal access token for the Calendly API
   * For production, you would need to implement OAuth flow
   */
  async getScheduledEvents(
    calendlyUserId: string,
    options: {
      startTime?: Date;
      endTime?: Date;
      status?: 'active' | 'canceled';
      count?: number;
    } = {}
  ): Promise<CalendlyEvent[]> {
    try {
      // For now, we'll use a mock implementation since we don't have API tokens
      // In production, you would make actual API calls to Calendly
      return this.getMockEvents(calendlyUserId, options);
    } catch (error) {
      console.error('Error fetching Calendly events:', error);
      throw new Error('Failed to fetch Calendly events');
    }
  }

  /**
   * Get event invitees for a specific event
   */
  async getEventInvitees(eventUri: string): Promise<CalendlyInvitee[]> {
    try {
      // Mock implementation
      return [];
    } catch (error) {
      console.error('Error fetching event invitees:', error);
      throw new Error('Failed to fetch event invitees');
    }
  }

  /**
   * Get event type information
   */
  async getEventType(eventTypeUri: string): Promise<CalendlyEventType | null> {
    try {
      // Mock implementation
      return null;
    } catch (error) {
      console.error('Error fetching event type:', error);
      return null;
    }
  }

  /**
   * Mock implementation for development/testing
   * In production, replace this with actual Calendly API calls
   */
  private getMockEvents(
    calendlyUserId: string,
    options: {
      startTime?: Date;
      endTime?: Date;
      status?: 'active' | 'canceled';
      count?: number;
    }
  ): CalendlyEvent[] {
    const now = new Date();
    const startTime = options.startTime || now;
    const endTime = options.endTime || new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days from now
    
    // Generate mock events for demonstration
    const mockEvents: CalendlyEvent[] = [
      {
        uri: `https://api.calendly.com/scheduled_events/mock-event-1-${calendlyUserId}`,
        name: '1:1 Coaching Session',
        meeting_notes_plain: 'Initial consultation and goal setting',
        status: 'active',
        start_time: new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000).toISOString(),
        end_time: new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000 + 45 * 60 * 1000).toISOString(),
        event_type: `https://api.calendly.com/event_types/mock-type-${calendlyUserId}`,
        location: {
          type: 'zoom',
          join_url: 'https://zoom.us/j/123456789'
        },
        invitees_counter: {
          total: 1,
          active: 1,
          limit: 1
        },
        created_at: now.toISOString(),
        updated_at: now.toISOString()
      },
      {
        uri: `https://api.calendly.com/scheduled_events/mock-event-2-${calendlyUserId}`,
        name: 'Progress Review Session',
        meeting_notes_plain: 'Review progress and adjust training plan',
        status: 'active',
        start_time: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        end_time: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000 + 30 * 60 * 1000).toISOString(),
        event_type: `https://api.calendly.com/event_types/mock-type-${calendlyUserId}`,
        location: {
          type: 'google_meet',
          join_url: 'https://meet.google.com/abc-defg-hij'
        },
        invitees_counter: {
          total: 1,
          active: 1,
          limit: 1
        },
        created_at: now.toISOString(),
        updated_at: now.toISOString()
      }
    ];

    return mockEvents.filter(event => {
      const eventStart = new Date(event.start_time);
      return eventStart >= startTime && eventStart <= endTime;
    });
  }

  /**
   * Extract Calendly event ID from URI
   */
  extractEventId(eventUri: string): string {
    const parts = eventUri.split('/');
    return parts[parts.length - 1];
  }

  /**
   * Extract Calendly user ID from event type URI
   */
  extractUserIdFromEventType(eventTypeUri: string): string {
    // This would need to be implemented based on actual Calendly API responses
    return 'mock-user-id';
  }
}

export const calendlyApi = new CalendlyApiService();
